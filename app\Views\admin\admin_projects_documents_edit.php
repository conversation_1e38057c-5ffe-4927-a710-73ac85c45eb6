<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/documents') ?>" class="btn btn-secondary">
    ← Back to Documents
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Edit Document
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Edit document information for project: <strong><?= esc($project['title']) ?></strong> (<?= esc($project['pro_code']) ?>)
        </p>
    </div>
</div>

<!-- Edit Form -->
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Document Information</h5>
            </div>
            <div class="card-body">
                <?php if (session()->getFlashdata('errors')): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                <li><?= esc($error) ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('error')): ?>
                    <div class="alert alert-danger">
                        <?= session()->getFlashdata('error') ?>
                    </div>
                <?php endif; ?>

                <form action="<?= base_url('admin/projects/' . $project['id'] . '/documents/' . $document['id'] . '/edit') ?>" 
                      method="post">
                    <?= csrf_field() ?>

                    <!-- Current File Information -->
                    <div class="alert alert-info mb-4">
                        <h6 class="alert-heading">Current File Information</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>File Name:</strong> <?= esc($document['file_name']) ?><br>
                                <strong>File Size:</strong> <?= number_format($document['file_size'] / 1024 / 1024, 2) ?> MB<br>
                                <strong>Uploaded:</strong> <?= date('M j, Y g:i A', strtotime($document['created_at'])) ?>
                            </div>
                            <div class="col-md-6">
                                <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents/' . $document['id'] . '/download') ?>" 
                                   class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-download"></i> Download Current File
                                </a>
                            </div>
                        </div>
                        <hr>
                        <small class="text-muted">
                            <strong>Note:</strong> You can only edit the document title and type. To replace the file, please delete this document and upload a new one.
                        </small>
                    </div>

                    <!-- Document Type -->
                    <div class="mb-3">
                        <label for="document_type" class="form-label">
                            Document Type <span class="text-danger">*</span>
                        </label>
                        <select class="form-select" id="document_type" name="document_type" required
                                style="border-color: #dc3545;">
                            <option value="">Select document type...</option>
                            <option value="proposal" <?= (old('document_type', $document['document_type']) === 'proposal') ? 'selected' : '' ?>>
                                Proposal
                            </option>
                            <option value="contract" <?= (old('document_type', $document['document_type']) === 'contract') ? 'selected' : '' ?>>
                                Contract
                            </option>
                            <option value="report" <?= (old('document_type', $document['document_type']) === 'report') ? 'selected' : '' ?>>
                                Report
                            </option>
                            <option value="evidence" <?= (old('document_type', $document['document_type']) === 'evidence') ? 'selected' : '' ?>>
                                Evidence
                            </option>
                            <option value="other" <?= (old('document_type', $document['document_type']) === 'other') ? 'selected' : '' ?>>
                                Other
                            </option>
                        </select>
                        <div class="form-text">
                            Select the type of document.
                        </div>
                    </div>

                    <!-- Document Title -->
                    <div class="mb-3">
                        <label for="title" class="form-label">
                            Document Title <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="title" name="title" 
                               value="<?= old('title', $document['title']) ?>" required maxlength="200"
                               style="border-color: #dc3545;"
                               placeholder="Enter a descriptive title for the document">
                        <div class="form-text">
                            Provide a clear, descriptive title for the document (max 200 characters).
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="d-flex justify-content-between">
                        <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents') ?>" 
                           class="btn btn-secondary">
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            💾 Update Document
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Document Type Information -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">📋 Document Types</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <span class="badge bg-primary me-2">Proposal</span> 
                                Project proposals, feasibility studies, design documents
                            </li>
                            <li class="mb-2">
                                <span class="badge bg-success me-2">Contract</span> 
                                Contracts, agreements, MOUs, legal documents
                            </li>
                            <li class="mb-2">
                                <span class="badge bg-info me-2">Report</span> 
                                Progress reports, final reports, evaluation reports
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <span class="badge bg-warning me-2">Evidence</span> 
                                Photos, certificates, proof documents, before/after images
                            </li>
                            <li class="mb-2">
                                <span class="badge bg-secondary me-2">Other</span> 
                                Any other project-related documents not covered above
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- File Replacement Information -->
        <div class="card mt-4">
            <div class="card-header bg-warning text-dark">
                <h6 class="card-title mb-0">⚠️ File Replacement</h6>
            </div>
            <div class="card-body">
                <p class="mb-2">
                    <strong>To replace the actual file:</strong>
                </p>
                <ol>
                    <li>Download the current file if you need to keep a copy</li>
                    <li>Delete this document using the delete button in the documents list</li>
                    <li>Upload the new file as a new document</li>
                </ol>
                <p class="text-muted mb-0">
                    This approach maintains a clear audit trail of all document changes and uploads.
                </p>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
