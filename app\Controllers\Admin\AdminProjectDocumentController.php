<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\ProjectModel;
use App\Models\ProjectDocumentModel;

/**
 * Admin Project Document Controller
 * 
 * Handles CRUD operations for project documents in the admin portal.
 * Uses RESTful approach with separate methods for each operation.
 */
class AdminProjectDocumentController extends BaseController
{
    protected $projectModel;
    protected $projectDocumentModel;

    public function __construct()
    {
        $this->projectModel = new ProjectModel();
        $this->projectDocumentModel = new ProjectDocumentModel();
    }

    /**
     * List documents for a project - GET request
     */
    public function index($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get documents for this project
        $documents = $this->projectDocumentModel->getByProject($projectId);

        // Get document statistics
        $documentStats = $this->projectDocumentModel->getDocumentStatistics($projectId);

        $data = [
            'title' => 'Project Documents - PROMIS Admin',
            'page_title' => 'Project Documents',
            'project' => $project,
            'documents' => $documents,
            'documentStats' => $documentStats
        ];

        return view('admin/admin_projects_documents_list', $data);
    }

    /**
     * Show create document form - GET request
     */
    public function create($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        $data = [
            'title' => 'Upload Document - PROMIS Admin',
            'page_title' => 'Upload Document',
            'project' => $project
        ];

        return view('admin/admin_projects_documents_create', $data);
    }

    /**
     * Store new document - POST request
     */
    public function store($projectId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');
        $adminUserId = session()->get('admin_user_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Validation rules
        $rules = [
            'document_type' => 'required|in_list[proposal,contract,report,evidence,other]',
            'title' => 'required|max_length[200]',
            'document_file' => 'uploaded[document_file]|max_size[document_file,15360]|ext_in[document_file,pdf,doc,docx,xls,xlsx,jpg,jpeg,png,gif]'
        ];

        $messages = [
            'document_type' => [
                'required' => 'Document type is required',
                'in_list' => 'Please select a valid document type'
            ],
            'title' => [
                'required' => 'Document title is required',
                'max_length' => 'Document title cannot exceed 200 characters'
            ],
            'document_file' => [
                'uploaded' => 'Please select a file to upload',
                'max_size' => 'File size cannot exceed 15MB',
                'ext_in' => 'Only PDF, DOC, DOCX, XLS, XLSX, JPG, JPEG, PNG, and GIF files are allowed'
            ]
        ];

        if (!$this->validate($rules, $messages)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Handle file upload
        $file = $this->request->getFile('document_file');
        if ($file && $file->isValid() && !$file->hasMoved()) {
            // Create upload directory if it doesn't exist
            $uploadDir = ROOTPATH . 'public/uploads/projects/documents/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            // Generate unique filename
            $newName = $file->getRandomName();
            
            try {
                // Move file to upload directory
                if ($file->move($uploadDir, $newName)) {
                    // Prepare document data
                    $documentData = [
                        'project_id' => $projectId,
                        'document_type' => $this->request->getPost('document_type'),
                        'title' => $this->request->getPost('title'),
                        'file_name' => $file->getClientName(),
                        'file_path' => 'public/uploads/projects/documents/' . $newName,
                        'file_size' => $file->getSize(),
                        'uploaded_by' => $adminUserId,
                        'created_by' => $adminUserId
                    ];

                    // Save to database
                    if ($this->projectDocumentModel->insert($documentData)) {
                        return redirect()->to(base_url('admin/projects/' . $projectId . '/documents'))
                                       ->with('success', 'Document uploaded successfully!');
                    } else {
                        // Delete uploaded file if database insert fails
                        unlink($uploadDir . $newName);
                        return redirect()->back()->withInput()
                                       ->with('error', 'Failed to save document information to database.');
                    }
                } else {
                    return redirect()->back()->withInput()
                                   ->with('error', 'Failed to upload file. Please try again.');
                }
            } catch (\Exception $e) {
                return redirect()->back()->withInput()
                               ->with('error', 'Error uploading file: ' . $e->getMessage());
            }
        } else {
            return redirect()->back()->withInput()
                           ->with('error', 'Invalid file or file upload failed.');
        }
    }

    /**
     * Show edit document form - GET request
     */
    public function edit($projectId, $documentId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get document
        $document = $this->projectDocumentModel->where('id', $documentId)
                                              ->where('project_id', $projectId)
                                              ->first();

        if (!$document) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/documents'))
                           ->with('error', 'Document not found.');
        }

        $data = [
            'title' => 'Edit Document - PROMIS Admin',
            'page_title' => 'Edit Document',
            'project' => $project,
            'document' => $document
        ];

        return view('admin/admin_projects_documents_edit', $data);
    }

    /**
     * Update document - POST request
     */
    public function update($projectId, $documentId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');
        $adminUserId = session()->get('admin_user_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get document
        $document = $this->projectDocumentModel->where('id', $documentId)
                                              ->where('project_id', $projectId)
                                              ->first();

        if (!$document) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/documents'))
                           ->with('error', 'Document not found.');
        }

        // Validation rules
        $rules = [
            'document_type' => 'required|in_list[proposal,contract,report,evidence,other]',
            'title' => 'required|max_length[200]'
        ];

        $messages = [
            'document_type' => [
                'required' => 'Document type is required',
                'in_list' => 'Please select a valid document type'
            ],
            'title' => [
                'required' => 'Document title is required',
                'max_length' => 'Document title cannot exceed 200 characters'
            ]
        ];

        if (!$this->validate($rules, $messages)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Prepare update data
        $updateData = [
            'document_type' => $this->request->getPost('document_type'),
            'title' => $this->request->getPost('title'),
            'updated_by' => $adminUserId
        ];

        try {
            if ($this->projectDocumentModel->update($documentId, $updateData)) {
                return redirect()->to(base_url('admin/projects/' . $projectId . '/documents'))
                               ->with('success', 'Document updated successfully!');
            } else {
                return redirect()->back()->withInput()
                               ->with('error', 'Failed to update document.');
            }
        } catch (\Exception $e) {
            return redirect()->back()->withInput()
                           ->with('error', 'Error updating document: ' . $e->getMessage());
        }
    }

    /**
     * Delete document - POST request
     */
    public function delete($projectId, $documentId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');
        $adminUserId = session()->get('admin_user_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get document
        $document = $this->projectDocumentModel->where('id', $documentId)
                                              ->where('project_id', $projectId)
                                              ->first();

        if (!$document) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/documents'))
                           ->with('error', 'Document not found.');
        }

        try {
            // Soft delete the document
            if ($this->projectDocumentModel->update($documentId, ['deleted_by' => $adminUserId])) {
                $this->projectDocumentModel->delete($documentId);
                return redirect()->to(base_url('admin/projects/' . $projectId . '/documents'))
                               ->with('success', 'Document deleted successfully!');
            } else {
                return redirect()->to(base_url('admin/projects/' . $projectId . '/documents'))
                               ->with('error', 'Failed to delete document.');
            }
        } catch (\Exception $e) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/documents'))
                           ->with('error', 'Error deleting document: ' . $e->getMessage());
        }
    }

    /**
     * Download document - GET request
     */
    public function download($projectId, $documentId)
    {
        // Check if user is logged in
        if (!session()->get('admin_user_id')) {
            return redirect()->to(base_url('auth/login'));
        }

        $adminOrganizationId = session()->get('admin_organization_id');

        // Get project with organization filter
        $project = $this->projectModel->where('id', $projectId)
                                     ->where('org_id', $adminOrganizationId)
                                     ->where('deleted_at', null)
                                     ->first();

        if (!$project) {
            return redirect()->to(base_url('admin/projects'))
                           ->with('error', 'Project not found or access denied.');
        }

        // Get document
        $document = $this->projectDocumentModel->where('id', $documentId)
                                              ->where('project_id', $projectId)
                                              ->first();

        if (!$document) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/documents'))
                           ->with('error', 'Document not found.');
        }

        // Check if file exists
        $filePath = ROOTPATH . $document['file_path'];
        if (!file_exists($filePath)) {
            return redirect()->to(base_url('admin/projects/' . $projectId . '/documents'))
                           ->with('error', 'File not found on server.');
        }

        // Force download
        return $this->response->download($filePath, null)->setFileName($document['file_name']);
    }
}
