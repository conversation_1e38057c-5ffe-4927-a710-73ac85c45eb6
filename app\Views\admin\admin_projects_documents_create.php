<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/documents') ?>" class="btn btn-secondary">
    ← Back to Documents
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Upload Document
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Upload a new document for project: <strong><?= esc($project['title']) ?></strong> (<?= esc($project['pro_code']) ?>)
        </p>
    </div>
</div>

<!-- Upload Form -->
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Document Information</h5>
            </div>
            <div class="card-body">
                <?php if (session()->getFlashdata('errors')): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                <li><?= esc($error) ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('error')): ?>
                    <div class="alert alert-danger">
                        <?= session()->getFlashdata('error') ?>
                    </div>
                <?php endif; ?>

                <form action="<?= base_url('admin/projects/' . $project['id'] . '/documents/create') ?>" 
                      method="post" enctype="multipart/form-data">
                    <?= csrf_field() ?>

                    <!-- Document Type -->
                    <div class="mb-3">
                        <label for="document_type" class="form-label">
                            Document Type <span class="text-danger">*</span>
                        </label>
                        <select class="form-select" id="document_type" name="document_type" required
                                style="border-color: #dc3545;">
                            <option value="">Select document type...</option>
                            <option value="proposal" <?= old('document_type') === 'proposal' ? 'selected' : '' ?>>
                                Proposal
                            </option>
                            <option value="contract" <?= old('document_type') === 'contract' ? 'selected' : '' ?>>
                                Contract
                            </option>
                            <option value="report" <?= old('document_type') === 'report' ? 'selected' : '' ?>>
                                Report
                            </option>
                            <option value="evidence" <?= old('document_type') === 'evidence' ? 'selected' : '' ?>>
                                Evidence
                            </option>
                            <option value="other" <?= old('document_type') === 'other' ? 'selected' : '' ?>>
                                Other
                            </option>
                        </select>
                        <div class="form-text">
                            Select the type of document you are uploading.
                        </div>
                    </div>

                    <!-- Document Title -->
                    <div class="mb-3">
                        <label for="title" class="form-label">
                            Document Title <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="title" name="title" 
                               value="<?= old('title') ?>" required maxlength="200"
                               style="border-color: #dc3545;"
                               placeholder="Enter a descriptive title for the document">
                        <div class="form-text">
                            Provide a clear, descriptive title for the document (max 200 characters).
                        </div>
                    </div>

                    <!-- File Upload -->
                    <div class="mb-3">
                        <label for="document_file" class="form-label">
                            Document File <span class="text-danger">*</span>
                        </label>
                        <input type="file" class="form-control" id="document_file" name="document_file" 
                               required accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.gif"
                               style="border-color: #dc3545;">
                        <div class="form-text">
                            <strong>Supported formats:</strong> PDF, DOC, DOCX, XLS, XLSX, JPG, JPEG, PNG, GIF<br>
                            <strong>Maximum file size:</strong> 15MB
                        </div>
                    </div>

                    <!-- File Upload Progress -->
                    <div class="mb-3" id="uploadProgress" style="display: none;">
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%"></div>
                        </div>
                        <small class="text-muted">Uploading file...</small>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="d-flex justify-content-between">
                        <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents') ?>" 
                           class="btn btn-secondary">
                            Cancel
                        </a>
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            📁 Upload Document
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Upload Guidelines -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">📋 Upload Guidelines</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Document Types:</h6>
                        <ul class="list-unstyled">
                            <li><span class="badge bg-primary me-2">Proposal</span> Project proposals, feasibility studies</li>
                            <li><span class="badge bg-success me-2">Contract</span> Contracts, agreements, MOUs</li>
                            <li><span class="badge bg-info me-2">Report</span> Progress reports, final reports</li>
                            <li><span class="badge bg-warning me-2">Evidence</span> Photos, certificates, proof documents</li>
                            <li><span class="badge bg-secondary me-2">Other</span> Any other project-related documents</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>File Requirements:</h6>
                        <ul class="list-unstyled">
                            <li>✅ Maximum file size: 15MB</li>
                            <li>✅ Supported formats: PDF, DOC, DOCX, XLS, XLSX</li>
                            <li>✅ Image formats: JPG, JPEG, PNG, GIF</li>
                            <li>✅ Files are stored securely and backed up</li>
                            <li>✅ Version tracking through upload timestamps</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// File upload validation and progress
document.getElementById('document_file').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        // Check file size (15MB = 15 * 1024 * 1024 bytes)
        const maxSize = 15 * 1024 * 1024;
        if (file.size > maxSize) {
            alert('File size exceeds 15MB limit. Please choose a smaller file.');
            e.target.value = '';
            return;
        }

        // Check file type
        const allowedTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/gif'
        ];
        
        if (!allowedTypes.includes(file.type)) {
            alert('File type not supported. Please choose a PDF, DOC, DOCX, XLS, XLSX, JPG, JPEG, PNG, or GIF file.');
            e.target.value = '';
            return;
        }

        // Auto-fill title if empty
        const titleField = document.getElementById('title');
        if (!titleField.value) {
            const fileName = file.name.replace(/\.[^/.]+$/, ""); // Remove extension
            titleField.value = fileName;
        }
    }
});

// Form submission with progress indication
document.querySelector('form').addEventListener('submit', function(e) {
    const submitBtn = document.getElementById('submitBtn');
    const progressDiv = document.getElementById('uploadProgress');
    
    // Disable submit button and show progress
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Uploading...';
    progressDiv.style.display = 'block';
    
    // Simulate progress (since we can't track actual upload progress easily)
    let progress = 0;
    const progressBar = progressDiv.querySelector('.progress-bar');
    const interval = setInterval(function() {
        progress += Math.random() * 15;
        if (progress > 90) progress = 90;
        progressBar.style.width = progress + '%';
    }, 200);
    
    // Clean up on form submission
    setTimeout(function() {
        clearInterval(interval);
        progressBar.style.width = '100%';
    }, 1000);
});
</script>

<?= $this->endSection() ?>
