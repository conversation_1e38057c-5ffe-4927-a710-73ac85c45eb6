<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('header_actions') ?>
<a href="<?= base_url('admin/projects/' . $project['id']) ?>" class="btn btn-secondary">
    ← Back to Project Profile
</a>
<a href="<?= base_url('admin/projects/' . $project['id'] . '/documents/create') ?>" class="btn btn-primary">
    📁 Upload Document
</a>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-xl">
    <div>
        <h1 style="color: var(--text-primary); font-size: 1.75rem; font-weight: 700; margin-bottom: var(--spacing-sm);">
            Project Documents
        </h1>
        <p style="color: var(--text-secondary); margin: 0;">
            Manage all documents for project: <strong><?= esc($project['title']) ?></strong> (<?= esc($project['pro_code']) ?>)
        </p>
    </div>
</div>

<!-- Document Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title"><?= $documentStats['total_documents'] ?? 0 ?></h4>
                        <p class="card-text">Total Documents</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-file-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title"><?= number_format(($documentStats['total_size'] ?? 0) / 1024 / 1024, 2) ?> MB</h4>
                        <p class="card-text">Total Size</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-hdd fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title"><?= $documentStats['recent_uploads'] ?? 0 ?></h4>
                        <p class="card-text">Recent Uploads</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-upload fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title"><?= number_format(($documentStats['average_size'] ?? 0) / 1024 / 1024, 2) ?> MB</h4>
                        <p class="card-text">Average Size</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-bar fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Documents List -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Documents</h5>
    </div>
    <div class="card-body">
        <?php if (!empty($documents)): ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Document</th>
                            <th>Type</th>
                            <th>File Size</th>
                            <th>Uploaded</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($documents as $document): ?>
                            <tr>
                                <td>
                                    <div>
                                        <strong><?= esc($document['title']) ?></strong>
                                        <br>
                                        <small class="text-muted"><?= esc($document['file_name']) ?></small>
                                    </div>
                                </td>
                                <td>
                                    <?php
                                    $typeLabels = [
                                        'proposal' => '<span class="badge bg-primary">Proposal</span>',
                                        'contract' => '<span class="badge bg-success">Contract</span>',
                                        'report' => '<span class="badge bg-info">Report</span>',
                                        'evidence' => '<span class="badge bg-warning">Evidence</span>',
                                        'other' => '<span class="badge bg-secondary">Other</span>'
                                    ];
                                    echo $typeLabels[$document['document_type']] ?? '<span class="badge bg-secondary">Unknown</span>';
                                    ?>
                                </td>
                                <td>
                                    <?= number_format($document['file_size'] / 1024 / 1024, 2) ?> MB
                                </td>
                                <td>
                                    <div>
                                        <?= date('M j, Y', strtotime($document['created_at'])) ?>
                                        <br>
                                        <small class="text-muted"><?= date('g:i A', strtotime($document['created_at'])) ?></small>
                                    </div>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents/' . $document['id'] . '/download') ?>" 
                                           class="btn btn-sm btn-outline-primary" title="Download">
                                            <i class="fas fa-download"></i>
                                        </a>
                                        <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents/' . $document['id'] . '/edit') ?>" 
                                           class="btn btn-sm btn-outline-secondary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="confirmDelete(<?= $document['id'] ?>, '<?= esc($document['title']) ?>')" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No Documents Found</h5>
                <p class="text-muted">No documents have been uploaded for this project yet.</p>
                <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents/create') ?>" class="btn btn-primary">
                    📁 Upload First Document
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the document "<span id="documentTitle"></span>"?</p>
                <p class="text-danger"><strong>This action cannot be undone.</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Delete Document</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(documentId, documentTitle) {
    document.getElementById('documentTitle').textContent = documentTitle;
    document.getElementById('deleteForm').action = '<?= base_url('admin/projects/' . $project['id'] . '/documents/') ?>' + documentId + '/delete';
    
    var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>

<?= $this->endSection() ?>
